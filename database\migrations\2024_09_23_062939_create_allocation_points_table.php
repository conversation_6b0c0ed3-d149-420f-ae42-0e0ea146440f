<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::create('allocation_points', function (Blueprint $table) {
        $table->id();
        $table->string('name');
        $table->text('location')->nullable();
        $table->enum('status', ['ACTIVE', 'INACTIVE'])->default('ACTIVE');
        $table->timestamps();
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('allocation_points');
    }
};
