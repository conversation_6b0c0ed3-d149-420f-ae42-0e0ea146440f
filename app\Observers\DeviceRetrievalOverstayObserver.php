<?php

namespace App\Observers;

use App\Models\DeviceRetrieval;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DeviceRetrievalOverstayObserver
{
    /**
     * Handle the DeviceRetrieval "creating" event.
     */
    public function creating(DeviceRetrieval $deviceRetrieval): void
    {
        Log::info('DeviceRetrievalOverstayObserver: Creating device retrieval', [
            'device_id' => $deviceRetrieval->device_id,
            'date' => $deviceRetrieval->date,
            'affixing_date' => $deviceRetrieval->affixing_date
        ]);

        $this->calculateAndUpdateOverstay($deviceRetrieval);
    }

    /**
     * Handle the DeviceRetrieval "created" event.
     */
    public function created(DeviceRetrieval $deviceRetrieval): void
    {
        Log::info('DeviceRetrievalOverstayObserver: Device retrieval created', [
            'id' => $deviceRetrieval->id,
            'overstay_days' => $deviceRetrieval->overstay_days,
            'overstay_amount' => $deviceRetrieval->overstay_amount
        ]);
    }

    /**
     * Handle the DeviceRetrieval "updating" event.
     */
    public function updating(DeviceRetrieval $deviceRetrieval): void
    {
        $relevantFields = ['date', 'affixing_date', 'long_route_id', 'manifest_date'];
        $changedFields = array_keys($deviceRetrieval->getDirty());
        $hasRelevantChanges = !empty(array_intersect($relevantFields, $changedFields));

        Log::info('DeviceRetrievalOverstayObserver: Updating device retrieval', [
            'id' => $deviceRetrieval->id,
            'device_id' => $deviceRetrieval->device_id,
            'changed_fields' => $changedFields,
            'relevant_fields_changed' => $hasRelevantChanges,
            'old_date' => $deviceRetrieval->getOriginal('date'),
            'new_date' => $deviceRetrieval->date,
            'old_affixing_date' => $deviceRetrieval->getOriginal('affixing_date'),
            'new_affixing_date' => $deviceRetrieval->affixing_date,
            'old_overstay_days' => $deviceRetrieval->getOriginal('overstay_days'),
            'old_overstay_amount' => $deviceRetrieval->getOriginal('overstay_amount')
        ]);

        // Only recalculate if relevant fields changed
        if ($hasRelevantChanges) {
            Log::info('DeviceRetrievalOverstayObserver: Recalculating overstay due to relevant field changes', [
                'id' => $deviceRetrieval->id,
                'changed_relevant_fields' => array_intersect($relevantFields, $changedFields)
            ]);
            
            $this->calculateAndUpdateOverstay($deviceRetrieval);
        }
    }

    /**
     * Handle the DeviceRetrieval "updated" event.
     */
    public function updated(DeviceRetrieval $deviceRetrieval): void
    {
        Log::info('DeviceRetrievalOverstayObserver: Device retrieval updated', [
            'id' => $deviceRetrieval->id,
            'final_overstay_days' => $deviceRetrieval->overstay_days,
            'final_overstay_amount' => $deviceRetrieval->overstay_amount
        ]);
    }

    /**
     * Calculate and update overstay days and amount
     */
    private function calculateAndUpdateOverstay(DeviceRetrieval $deviceRetrieval): void
    {
        try {
            Log::info('DeviceRetrievalOverstayObserver: Starting overstay calculation', [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                'device_id' => $deviceRetrieval->device_id,
                'date' => $deviceRetrieval->date,
                'affixing_date' => $deviceRetrieval->affixing_date,
                'manifest_date' => $deviceRetrieval->manifest_date,
                'long_route_id' => $deviceRetrieval->long_route_id
            ]);

            // Determine the reference date for calculation
            $referenceDate = $this->determineReferenceDate($deviceRetrieval);
            
            if (!$referenceDate) {
                Log::warning('DeviceRetrievalOverstayObserver: No valid reference date found', [
                    'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                    'device_id' => $deviceRetrieval->device_id
                ]);
                
                $deviceRetrieval->overstay_days = 0;
                $deviceRetrieval->overstay_amount = 0.00;
                return;
            }

            // Calculate overstay days
            $overstayDays = $this->calculateOverstayDays($referenceDate, $deviceRetrieval);
            
            // Calculate overstay amount
            $overstayAmount = $this->calculateOverstayAmount($overstayDays);

            Log::info('DeviceRetrievalOverstayObserver: Overstay calculation completed', [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                'device_id' => $deviceRetrieval->device_id,
                'reference_date' => $referenceDate->toDateString(),
                'current_date' => now()->toDateString(),
                'days_difference' => now()->startOfDay()->diffInDays($referenceDate->startOfDay()),
                'grace_period' => $deviceRetrieval->long_route_id ? 2 : 1,
                'calculated_overstay_days' => $overstayDays,
                'calculated_overstay_amount' => $overstayAmount,
                'old_overstay_days' => $deviceRetrieval->overstay_days,
                'old_overstay_amount' => $deviceRetrieval->overstay_amount
            ]);

            // Update the model
            $deviceRetrieval->overstay_days = $overstayDays;
            $deviceRetrieval->overstay_amount = $overstayAmount;

        } catch (\Exception $e) {
            Log::error('DeviceRetrievalOverstayObserver: Error calculating overstay', [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                'device_id' => $deviceRetrieval->device_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Set safe defaults on error
            $deviceRetrieval->overstay_days = 0;
            $deviceRetrieval->overstay_amount = 0.00;
        }
    }

    /**
     * Determine the reference date for overstay calculation
     * Priority: affixing_date > manifest_date > date
     */
    private function determineReferenceDate(DeviceRetrieval $deviceRetrieval): ?Carbon
    {
        $dates = [
            'affixing_date' => $deviceRetrieval->affixing_date,
            'manifest_date' => $deviceRetrieval->manifest_date,
            'date' => $deviceRetrieval->date
        ];

        foreach ($dates as $field => $date) {
            if ($date) {
                $carbonDate = $date instanceof Carbon ? $date : Carbon::parse($date);
                
                Log::info('DeviceRetrievalOverstayObserver: Using reference date', [
                    'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                    'field_used' => $field,
                    'reference_date' => $carbonDate->toDateString()
                ]);
                
                return $carbonDate;
            }
        }

        return null;
    }

    /**
     * Calculate overstay days based on reference date and grace period
     */
    private function calculateOverstayDays(Carbon $referenceDate, DeviceRetrieval $deviceRetrieval): int
    {
        // Determine grace period based on route type
        $gracePeriod = $deviceRetrieval->long_route_id ? 2 : 1; // 2 days for long route, 1 for normal
        
        // Calculate days difference from reference date to now
        $daysDifference = now()->startOfDay()->diffInDays($referenceDate->startOfDay());
        
        // Calculate overstay days (subtract grace period)
        $overstayDays = max(0, $daysDifference - $gracePeriod);

        Log::info('DeviceRetrievalOverstayObserver: Overstay days calculation', [
            'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
            'reference_date' => $referenceDate->toDateString(),
            'current_date' => now()->toDateString(),
            'days_difference' => $daysDifference,
            'grace_period' => $gracePeriod,
            'route_type' => $deviceRetrieval->long_route_id ? 'long' : 'normal',
            'calculated_overstay_days' => $overstayDays
        ]);

        return $overstayDays;
    }

    /**
     * Calculate overstay amount based on overstay days
     * Business Rule: D1000 per day starting from day 1
     * 1 day = D1000, 3 days = D3000
     */
    private function calculateOverstayAmount(int $overstayDays): float
    {
        if ($overstayDays <= 0) {
            return 0.00;
        }

        // D1000 per day of overstay
        $baseAmount = 1000.00;
        $totalAmount = $baseAmount * $overstayDays;

        Log::info('DeviceRetrievalOverstayObserver: Overstay amount calculation', [
            'overstay_days' => $overstayDays,
            'base_amount_per_day' => $baseAmount,
            'calculated_total_amount' => $totalAmount
        ]);

        return $totalAmount;
    }
}
