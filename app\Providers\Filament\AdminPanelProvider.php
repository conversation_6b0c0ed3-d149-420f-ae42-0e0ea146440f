<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Resources\AllocationPointResource;
use App\Filament\Resources\AssignToAgentResource;
use App\Filament\Resources\ConfirmedAffixedResource;
use App\Filament\Resources\DataEntryAssignmentResource;
use App\Filament\Resources\DeviceRetrievalResource;
use App\Filament\Resources\DeviceResource;
use App\Filament\Resources\DestinationResource;
use App\Filament\Resources\DistributionPointResource;
use App\Filament\Resources\InvoiceResource;
use App\Filament\Resources\LongRouteResource;
use App\Filament\Resources\MonitoringResource;
use App\Filament\Resources\NotificationResource;
use App\Filament\Resources\OtherItemResource;
use App\Filament\Resources\PermissionResource;
use App\Filament\Resources\RegimeResource;
use App\Filament\Resources\ReportResource;
use App\Filament\Resources\RoleResource;
use App\Filament\Resources\RouteResource;
use App\Filament\Resources\StoreResource;
use App\Filament\Resources\TransferResource;
use App\Filament\Resources\UserResource;
use App\Models\AllocationPoint;
use App\Models\DataEntryAssignment;
use App\Models\DistributionPoint;
use Filament\Facades\Filament;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->passwordReset()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
               // Widgets\AccountWidget::class,
               // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigation(function (NavigationBuilder $builder): NavigationBuilder {
                $user = auth()->user();

                if (!$user) {
                    return $builder;
                }

                $isSuperAdmin = $user->hasRole('Super Admin');
                $isWarehouseManager = $user->hasRole('Warehouse Manager');
                $isAllocationOfficer = $user->hasRole('Allocation Officer');
                $isDataEntryOfficer = $user->hasRole('Data Entry Officer');
                $isDistributionOfficer = $user->hasRole('Distribution Officer');
                $isMonitoringOfficer = $user->hasRole('Monitoring Officer');
                $isAffixingOfficer = $user->hasRole('Affixing Officer');
                $isRetrievalOfficer = $user->hasRole('Retrieval Officer');
                $isFinanceOfficer = $user->hasRole('Finance Officer');

                // Base navigation for all users
                $builder->item(
                    NavigationItem::make('Dashboard')
                        ->icon('heroicon-o-home')
                        ->isActiveWhen(fn (): bool => request()->routeIs('filament.admin.pages.dashboard'))
                        ->url(fn (): string => Pages\Dashboard::getUrl())
                );

                // Finance Officer navigation
                if ($isFinanceOfficer) {
                    $builder->group('Finance Management', [
                        NavigationItem::make('Invoices')
                            ->icon('heroicon-o-document-text')
                            ->url(\App\Filament\Resources\InvoiceResource::getUrl())
                            ->badge(
                                fn () => \App\Models\Invoice::where('status', 'pending')->count() ?: null,
                                color: fn () => 'warning'
                            ),
                    ]);
                }

                // Data Entry Officer navigation - Only show Data Entry/Assignment menu
                if ($isDataEntryOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $builder->group('Data Entry/Assignment',
                        DataEntryAssignment::with('allocationPoint')
                            ->whereHas('allocationPoint', function ($query) {
                                $userPermissions = auth()->user()->permissions;

                                $allowedPoints = collect($userPermissions)
                                    ->filter(fn ($permission) => str_starts_with($permission->name, 'view_data_entry_'))
                                    ->map(function ($permission) {
                                        $slug = Str::after($permission->name, 'view_data_entry_');
                                        return Str::title(str_replace('-', ' ', $slug));
                                    })
                                    ->toArray();

                                $query->whereIn('name', $allowedPoints);
                            })
                            ->get()
                            ->map(fn (DataEntryAssignment $assignment) =>
                                NavigationItem::make($assignment->allocationPoint->name)
                                    ->icon('heroicon-o-document')
                                    ->url(DataEntryAssignmentResource::getUrl('view', ['record' => $assignment]))
                            )
                            ->toArray()
                    );

                    // Return here to prevent showing other menus
                    return $builder;
                }

                // Monitoring Officer Navigation
                if ($isMonitoringOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $builder->group('Monitoring', [
                        NavigationItem::make('Device Monitoring')
                            ->icon('heroicon-o-building-office')
                            ->url(MonitoringResource::getUrl()),
                    ]);

                    return $builder;
                }

                // Affixing Officer Navigation
                if ($isAffixingOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $builder->group('Confirmed Dispatch', [
                        NavigationItem::make('Confirmed Affixed')
                            ->icon('heroicon-o-check-circle')
                            ->url(ConfirmedAffixedResource::getUrl()),
                    ]);

                    return $builder;
                }

                // Distribution Officer Navigation
                if ($isDistributionOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $distributionPoints = DistributionPoint::all()
                        ->map(fn (DistributionPoint $point) =>
                            NavigationItem::make($point->name)
                                ->icon('heroicon-o-building-office')
                                ->url(DistributionPointResource::getUrl('view', ['record' => $point]))
                                ->badge(
                                    DistributionPoint::getBadgeText($point->id),
                                    color: function() use ($point) {
                                        $config = DistributionPoint::getBadgeConfig($point->id);
                                        // Use the color of the majority as the badge color
                                        return $config['receivedRatio'] > $config['otherRatio'] ? 'danger' : 'warning';
                                    }
                                )
                        )
                        ->toArray();

                    $builder->group('Distribution Points', $distributionPoints);

                    return $builder;
                }

                // Retrieval Officer Navigation
                if ($isRetrievalOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $builder->group('Device Retrieval', [
                        NavigationItem::make('Device Retrievals')
                            ->icon('heroicon-o-arrow-uturn-left')
                            ->url(DeviceRetrievalResource::getUrl()),
                    ]);

                    return $builder;
                }

                // Allocation Officer navigation
                if ($isAllocationOfficer && !$isSuperAdmin && !$isWarehouseManager) {
                    $allocationPoints = AllocationPoint::all()
                        ->filter(function (AllocationPoint $point) {
                            $permissionName = 'view_allocationpoint_' . Str::slug($point->name);
                            return auth()->user()->hasPermissionTo($permissionName);
                        })
                        ->map(fn (AllocationPoint $point) =>
                            NavigationItem::make($point->name)
                                ->icon('heroicon-o-home')
                                ->url(AllocationPointResource::getUrl('view', ['record' => $point->id]))
                                ->badge(
                                    AllocationPoint::getBadgeText($point->id),
                                    color: AllocationPoint::getBadgeColor($point->id)
                                )
                        )
                        ->toArray();

                    $builder->group('Allocation', $allocationPoints);

                    return $builder;
                }

                // Admin and Warehouse Manager navigation (full access)
                if ($isSuperAdmin || $isWarehouseManager) {
                    // Inventory Management
                    $builder->group('Inventory Management', [
                        NavigationItem::make('Devices/Trackers')
                            ->icon('heroicon-o-device-phone-mobile')
                            ->url(DeviceResource::getUrl()),
                        NavigationItem::make('Stores/Device Stock')
                            ->icon('heroicon-o-archive-box')
                            ->url(StoreResource::getUrl()),
                        NavigationItem::make('Transfers')
                            ->icon('heroicon-o-map')
                            ->url(TransferResource::getUrl()),
                        NavigationItem::make('Other Items')
                            ->icon('heroicon-o-arrow-path')
                            ->url(OtherItemResource::getUrl()),
                    ]);

                    // Distribution Points
                    $distributionPoints = DistributionPoint::all()
                        ->map(fn (DistributionPoint $point) =>
                            NavigationItem::make($point->name)
                                ->icon('heroicon-o-building-office')
                                ->url(DistributionPointResource::getUrl('view', ['record' => $point]))
                                ->badge(
                                    DistributionPoint::getBadgeText($point->id),
                                    color: DistributionPoint::getDetailedBadgeColor($point->id)
                                )
                        )
                        ->toArray();

                    $builder->group('Distribution Points', $distributionPoints);

                    // Allocation Points
                    $allocationPoints = AllocationPoint::all()
                        ->map(fn (AllocationPoint $point) =>
                            NavigationItem::make($point->name)
                                ->icon('heroicon-o-home')
                                ->url(AllocationPointResource::getUrl('view', ['record' => $point->id]))
                                ->badge(
                                    AllocationPoint::getBadgeText($point->id),
                                    color: AllocationPoint::getBadgeColor($point->id)
                                )
                        )
                        ->toArray();

                    $builder->group('Allocation Points', $allocationPoints);

                    // Data Entry/Assignment
                    $dataEntryAssignments = DataEntryAssignment::with('allocationPoint')
                        ->whereHas('allocationPoint')
                        ->get()
                        ->map(fn (DataEntryAssignment $assignment) =>
                            NavigationItem::make($assignment->allocationPoint->name)
                                ->icon('heroicon-o-document')
                                ->url(DataEntryAssignmentResource::getUrl('view', ['record' => $assignment]))
                        )
                        ->toArray();

                    $builder->group('Data Entry/Assignment', $dataEntryAssignments);

                    // Confirmed Dispatch
                    $builder->group('Confirmed Dispatch', [
                        NavigationItem::make('Confirmed Affixed')
                            ->icon('heroicon-o-check-circle')
                            ->url(ConfirmedAffixedResource::getUrl()),
                    ]);

                    // Device Retrievals
                    $builder->group('Device Retrievals', [
                        NavigationItem::make('Device Retrievals')
                            ->icon('heroicon-o-arrow-uturn-left')
                            ->url(DeviceRetrievalResource::getUrl()),
                    ]);

                    // Reports
                    $builder->group('Reports', [
                        NavigationItem::make('All Reports')
                            ->icon('heroicon-o-document-chart-bar')
                            ->url(ReportResource::getUrl()),
                    ]);

                    // Monitoring
                    $builder->group('Monitoring', [
                        NavigationItem::make('Monitoring')
                            ->icon('heroicon-o-chart-bar')
                            ->url(MonitoringResource::getUrl('index')),
                    ]);

                    // Notifications (Super Admin only)
                    if ($isSuperAdmin) {
                        $builder->group('Notifications', [
                            NavigationItem::make('All Notifications')
                                ->icon('heroicon-o-bell')
                                ->url(NotificationResource::getUrl())
                                ->badge(NotificationResource::getNavigationBadge(), color: 'warning'),
                        ]);
                    }

                    // Configuration
                    $configItems = [
                        NavigationItem::make('Distribution Points')
                            ->icon('heroicon-o-building-office')
                            ->url(DistributionPointResource::getUrl()),
                        NavigationItem::make('Routes')
                            ->icon('heroicon-o-map')
                            ->url(RouteResource::getUrl()),
                        NavigationItem::make('Long Routes')
                            ->icon('heroicon-o-map')
                            ->url(LongRouteResource::getUrl()),
                        NavigationItem::make('Allocation Points')
                            ->icon('heroicon-o-building-storefront')
                            ->url(AllocationPointResource::getUrl()),
                        NavigationItem::make('Regimes')
                            ->icon('heroicon-o-clipboard-document-list')
                            ->url(RegimeResource::getUrl()),
                        NavigationItem::make('Destinations')
                            ->icon('heroicon-o-map-pin')
                            ->url(DestinationResource::getUrl()),
                    ];

                    // Add user management items for Super Admin only
                    if ($isSuperAdmin) {
                        array_unshift($configItems,
                            NavigationItem::make('Users')
                                ->icon('heroicon-o-users')
                                ->url(UserResource::getUrl()),
                            NavigationItem::make('Roles')
                                ->icon('heroicon-o-shield-check')
                                ->url(RoleResource::getUrl()),
                            NavigationItem::make('Permissions')
                                ->icon('heroicon-o-key')
                                ->url(PermissionResource::getUrl())
                        );
                    }

                    $builder->group('Configuration', $configItems);
                }

                return $builder;
            });
    }

    public function boot()
    {
        // We're not using this method for navigation anymore
        // All navigation is handled in the navigation() method above
    }
}
