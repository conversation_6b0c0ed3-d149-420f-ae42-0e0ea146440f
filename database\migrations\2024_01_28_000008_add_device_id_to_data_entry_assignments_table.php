<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('data_entry_assignments', function (Blueprint $table) {
            $table->foreignId('device_id')->nullable()->after('allocation_point_id')
                  ->constrained('devices')->nullOnDelete();
        });
    }

    public function down()
    {
        Schema::table('data_entry_assignments', function (Blueprint $table) {
            $table->dropForeign(['device_id']);
            $table->dropColumn('device_id');
        });
    }
};