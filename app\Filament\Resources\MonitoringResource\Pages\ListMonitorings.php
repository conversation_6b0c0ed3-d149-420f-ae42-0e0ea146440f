<?php

namespace App\Filament\Resources\MonitoringResource\Pages;

use App\Filament\Resources\MonitoringResource;
use App\Models\Monitoring;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Actions\Action;
use Filament\Forms;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Database\Eloquent\Collection;
use Filament\Support\Colors\Color;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ListMonitorings extends ListRecords
{
    protected static string $resource = MonitoringResource::class;
    protected static string $view = 'filament.resources.monitoring-resource.pages.list-monitorings';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('overdue')
                ->label('Overdue Devices')
                ->color(Color::Red)
                ->icon('heroicon-o-exclamation-circle')
                ->action(function () {
                    $this->resetTableFiltersForm();
                    $this->tableFilters = array_merge($this->tableFilters, ['overdue' => ['value' => true]]);
                })
                ->button(),
        ];
    }

    public function applyFilter(string $type): void
    {
        $this->tableFilters[$type] = true;
        $this->resetPage();
    }

    protected function getTableActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Action::make('addNote')
                    ->label('Add Note')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        Forms\Components\Textarea::make('note')
                            ->label('Note')
                            ->required(),
                        Forms\Components\DateTimePicker::make('manifest_date')
                            ->label('Manifest Date')
                    ])
                    ->action(function (Monitoring $record, array $data): void {
                        try {
                            // Use our new magic method! 
                            $success = $record->addNewNote(
                                $data['note'],
                                $data['manifest_date'] ?? null
                            );

                            if ($success) {
                                Notification::make()
                                    ->success()
                                    ->title('Note Added')
                                    ->body('Your note has been saved successfully!')
                                    ->send();
                            } else {
                                throw new \Exception('Failed to save note');
                            }

                        } catch (\Exception $e) {
                            Notification::make()
                                ->danger()
                                ->title('Error')
                                ->body('Could not add the note: ' . $e->getMessage())
                                ->send();
                        }
                    })
                    ->modalWidth('md'),
                Tables\Actions\DeleteAction::make()
            ])
        ];
    }

    public function table(Table $table): Table
    {
        $query = static::getResource()::getEloquentQuery()
            ->with(['device', 'route', 'longRoute']);

        return $table
            ->query($query)
            ->columns([
                Tables\Columns\CheckboxColumn::make('selected')
                    ->label('Select')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Dispatch Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('current_date')
                    ->label('Current Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('device.device_id')
                    ->label('Device ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sad_number')
                    ->label('SAD/T1')
                    ->sortable()
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('vehicle_number')
                    ->label('Vehicle No.')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('regime')
                    ->label('Regime')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('route.name')
                    ->label('Route')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('longRoute.name')
                    ->label('Long Route')
                    ->searchable(),
                Tables\Columns\TextColumn::make('manifest_date')
                    ->label('Manifest Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('note')
                    ->label('Note')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('destination')
                    ->label('Destination')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('agency')
                    ->label('Agency')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('agent_contact')
                    ->label('Agent Contact')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('truck_number')
                    ->label('Truck No.')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver_name')
                    ->label('Driver Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('affixing_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('overdue_days')
                    ->label('Overdue DAYS')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('pending')
                    ->label('Pending Devices')
                    ->placeholder('All Devices')
                    ->trueLabel('Show Only Pending')
                    ->falseLabel('Show Only Non-Pending')
                    ->queries(
                        true: fn ($query) => $query->whereNull('manifest_date'),
                        false: fn ($query) => $query->whereNotNull('manifest_date'),
                    ),
                Tables\Filters\TernaryFilter::make('overdue')
                    ->label('Overdue Devices')
                    ->placeholder('All Devices')
                    ->trueLabel('Show Only Overdue')
                    ->falseLabel('Show Only Non-Overdue')
                    ->queries(
                        true: fn ($query) => $query->where('overdue_hours', '>', 0),
                        false: fn ($query) => $query->where('overdue_hours', '=', 0),
                    ),
            ])
            ->actions([
                Tables\Actions\Action::make('addNote')
                    ->label('Add Note')
                    ->icon('heroicon-o-pencil-square')
                    ->form([
                        Forms\Components\Textarea::make('note')
                            ->label('Note')
                            ->required()
                            ->maxLength(1000),
                        Forms\Components\DateTimePicker::make('manifest_date')
                            ->label('Manifest Date')
                    ])
                    ->action(function (Monitoring $record, array $data): void {
                        try {
                            // Use our new magic method! 
                            $success = $record->addNewNote(
                                $data['note'],
                                $data['manifest_date'] ?? null
                            );

                            if ($success) {
                                Notification::make()
                                    ->success()
                                    ->title('Note Added')
                                    ->body('Your note has been saved successfully!')
                                    ->send();
                            } else {
                                throw new \Exception('Failed to save note');
                            }

                        } catch (\Exception $e) {
                            Notification::make()
                                ->danger()
                                ->title('Error')
                                ->body('Could not add the note: ' . $e->getMessage())
                                ->send();
                        }
                    })
                    ->modalWidth('md'),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading('Delete Record')
                    ->modalDescription('Are you sure you want to delete this record? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, Delete')
                    ->modalCancelActionLabel('No, Cancel'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                Tables\Actions\BulkAction::make('process')
                    ->label('Process Selected')
                    ->action(function (Collection $records) {
                        // Add your processing logic here
                    })
                    ->deselectRecordsAfterCompletion()
            ])
            ->defaultSort('date', 'desc')
            ->poll('10s');
    }
}