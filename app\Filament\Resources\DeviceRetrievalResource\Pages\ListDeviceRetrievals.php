<?php

namespace App\Filament\Resources\DeviceRetrievalResource\Pages;

use App\Filament\Resources\DeviceRetrievalResource;
use App\Filament\Actions\OverdueBillAction;
use App\Filament\Actions\FinanceApprovalAction;
use App\Filament\Actions\OverdueBillsAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\Device;
use App\Models\DistributionPoint;
use Filament\Forms;
use Filament\Support\Colors\Color;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;

class ListDeviceRetrievals extends ListRecords
{
    protected static string $resource = DeviceRetrievalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('device.device_id')
                    ->label('Device ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('boe')
                    ->label('BOE')
                    ->searchable(),
                Tables\Columns\TextColumn::make('vehicle_number')
                    ->label('Vehicle Number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('regime')
                    ->label('Regime')
                    ->searchable(),
                Tables\Columns\TextColumn::make('destination')
                    ->label('Destination')
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('retrieval_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'NOT_RETRIEVED' => 'warning',
                        'RETRIEVED' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('overstay_days')
                    ->label('Overstay Days')
                    ->sortable()
                    ->color(fn ($state) => $state > 0 ? 'danger' : 'success'),
                Tables\Columns\TextColumn::make('overstay_amount')
                    ->label('Overstay Amount')
                    ->money('GMD')
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'PP' => 'danger',
                        'PD' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'PP' => 'Pending Payment',
                        'PD' => 'Paid',
                        default => $state,
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('transfer_status')
                    ->options([
                        'pending' => 'Transfer Pending',
                        'completed' => 'Transfer Completed',
                    ]),
                Tables\Filters\SelectFilter::make('payment_status')
                    ->options([
                        'PP' => 'Pending Payment',
                        'PD' => 'Paid',
                    ]),
                Tables\Filters\Filter::make('overstay_days')
                    ->form([
                        Forms\Components\TextInput::make('min')
                            ->label('Minimum Overstay Days')
                            ->numeric(),
                        Forms\Components\TextInput::make('max')
                            ->label('Maximum Overstay Days')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min'],
                                fn (Builder $query, $min): Builder => $query->where('overstay_days', '>=', $min)
                            )
                            ->when(
                                $data['max'],
                                fn (Builder $query, $max): Builder => $query->where('overstay_days', '<=', $max)
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    // Return to Outstation action
                    Tables\Actions\Action::make('returnToOutstation')
                        ->label('Return to Outstation')
                        ->icon('heroicon-o-arrow-uturn-left')
                        ->color('warning')
                        ->form([
                            Forms\Components\Select::make('distribution_point_id')
                                ->label('Select Distribution Point')
                                ->options(function () {
                                    return DistributionPoint::select('id', 'name')
                                        ->orderBy('name')
                                        ->pluck('name', 'id');
                                })
                                ->required()
                                ->searchable()
                        ])
                        ->action(function ($record, array $data): void {
                            try {
                                DB::beginTransaction();

                                DB::transaction(function () use ($record, $data) {
                                    // Update device status
                                    DB::table('devices')
                                        ->where('id', $record->device_id)
                                        ->update([
                                            'status' => 'PENDING',
                                            'distribution_point_id' => $data['distribution_point_id'],
                                            'updated_at' => now()
                                        ]);

                                    // Update retrieval status to RETURNED
                                    DB::table('device_retrievals')
                                        ->where('id', $record->id)
                                        ->update([
                                            'retrieval_status' => 'RETURNED',
                                            'transfer_status' => 'pending',
                                            'distribution_point_id' => $data['distribution_point_id'],
                                            'updated_at' => now()
                                        ]);
                                });

                                DB::commit();

                                Notification::make()
                                    ->success()
                                    ->title('Device Return Initiated')
                                    ->send();

                            } catch (\Exception $e) {
                                DB::rollBack();
                                Log::error('Error in returnToOutstation', [
                                    'error' => $e->getMessage(),
                                    'device_retrieval_id' => $record->id
                                ]);

                                Notification::make()
                                    ->danger()
                                    ->title('Error')
                                    ->body('Failed to initiate device return: ' . $e->getMessage())
                                    ->send();
                            }
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Return Device to Outstation')
                        ->visible(fn ($record): bool =>
                            $record->retrieval_status === 'RETRIEVED' &&
                            $record->transfer_status !== 'completed' &&
                            auth()->user()?->hasAnyRole([
                                'Super Admin',
                                'Warehouse Manager',
                                'Retrieval Officer'
                            ])
                        ),

                    // Overdue Bills action
                    OverdueBillsAction::make()
                        ->visible(fn ($record) =>
                            $record->overstay_days >= 2 &&
                            $record->payment_status !== 'PD'
                        ),

                    // Retrieve Device action
                    Tables\Actions\Action::make('retrieveDevice')
                        ->label('Retrieve Device')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn ($record): bool =>
                            $record->retrieval_status === 'NOT_RETRIEVED' &&
                            auth()->user()?->hasAnyRole([
                                'Super Admin',
                                'Warehouse Manager',
                                'Retrieval Officer'
                            ])
                        )
                        ->form(function ($record) {
                            // Check if user is Super Admin or Warehouse Manager
                            $isPrivilegedUser = auth()->user()?->hasRole(['Super Admin', 'Warehouse Manager']);

                            // Check if device is overdue
                            $isOverdue = $record->overdue_days > 0;
                            $isLongRoute = $record->long_route_id !== null;
                            $minDays = $isLongRoute ? 2 : 1;
                            $requiresReceipt = $isOverdue && $record->overdue_days >= $minDays;

                            // If user is privileged, or device isn't overdue, return empty form
                            if ($isPrivilegedUser || !$requiresReceipt) {
                                return [];
                            }

                            // For other users with overdue devices, show receipt input
                            return [
                                TextInput::make('receipt_number')
                                    ->label('Receipt Number')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText("Device is overdue by {$record->overdue_days} days. Receipt number is required.")
                            ];
                        })
                        ->action(function ($record, array $data): void {
                            try {
                                DB::beginTransaction();

                                $isPrivilegedUser = auth()->user()?->hasRole(['Super Admin', 'Warehouse Manager']);
                                $isOverdue = $record->overdue_days > 0;
                                $isLongRoute = $record->long_route_id !== null;
                                $minDays = $isLongRoute ? 2 : 1;
                                $requiresReceipt = $isOverdue && $record->overdue_days >= $minDays;

                                // Check if receipt is required but not provided
                                if (!$isPrivilegedUser && $requiresReceipt && empty($data['receipt_number'])) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Receipt Required')
                                        ->body('This device is overdue. Please provide a receipt number.')
                                        ->send();
                                    return;
                                }

                                // Check if device can be retrieved based on overdue status and payment
                                if (!$record->canBeRetrieved()) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Payment Required')
                                        ->body('This device has overdue fees. Payment must be completed before retrieval.')
                                        ->send();
                                    return;
                                }

                                // Update device retrieval status
                                $updateData = [
                                    'retrieval_status' => 'RETRIEVED',
                                    'updated_at' => now()
                                ];

                                // Add receipt number if provided
                                if (!empty($data['receipt_number'])) {
                                    $updateData['receipt_number'] = $data['receipt_number'];
                                }

                                // Update device retrieval
                                DB::table('device_retrievals')
                                    ->where('id', $record->id)
                                    ->update($updateData);

                                // Update device status
                                DB::table('devices')
                                    ->where('id', $record->device_id)
                                    ->update([
                                        'status' => 'RETRIEVED',
                                        'updated_at' => now()
                                    ]);

                                DB::commit();

                                Notification::make()
                                    ->success()
                                    ->title('Device Retrieved')
                                    ->body('Device has been successfully retrieved.')
                                    ->send();

                            } catch (\Exception $e) {
                                DB::rollBack();
                                Log::error('Device retrieval failed', [
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString(),
                                    'device_retrieval_id' => $record->id
                                ]);

                                Notification::make()
                                    ->danger()
                                    ->title('Error')
                                    ->body('Failed to retrieve device: ' . $e->getMessage())
                                    ->send();
                            }
                        })
                        ->modalHeading(function ($record) {
                            $isPrivilegedUser = auth()->user()?->hasRole(['Super Admin', 'Warehouse Manager']);
                            $isOverdue = $record->overdue_days > 0;
                            $isLongRoute = $record->long_route_id !== null;
                            $minDays = $isLongRoute ? 2 : 1;

                            if (!$isPrivilegedUser && $isOverdue && $record->overdue_days >= $minDays) {
                                return 'Retrieve Overdue Device';
                            }
                            return 'Retrieve Device';
                        })
                        ->modalDescription(function ($record) {
                            $isPrivilegedUser = auth()->user()?->hasRole(['Super Admin', 'Warehouse Manager']);
                            $isOverdue = $record->overdue_days > 0;
                            $isLongRoute = $record->long_route_id !== null;
                            $minDays = $isLongRoute ? 2 : 1;

                            if (!$isPrivilegedUser && $isOverdue && $record->overdue_days >= $minDays) {
                                return "This device is overdue by {$record->overdue_days} days. Please provide a receipt number.";
                            }
                            return 'Are you sure you want to retrieve this device?';
                        })
                        ->requiresConfirmation(),



                    // Finance Approval action
                    Tables\Actions\Action::make('finance_approval')
                        ->label('Approve Payment')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn ($record) =>
                            // Only show to Finance Officers and Super Admins
                            (auth()->user()->hasRole('Finance Officer') || auth()->user()->hasRole('Super Admin')) &&
                            // Only show for pending payment records
                            $record->payment_status === 'PP' &&
                            // Only show if there's an amount to approve
                            $record->overstay_amount > 0 &&
                            // IMPORTANT: Don't show to Retrieval Officers who aren't also Finance Officers or Super Admins
                            !(auth()->user()->hasRole('Retrieval Officer') &&
                              !auth()->user()->hasRole('Finance Officer') &&
                              !auth()->user()->hasRole('Super Admin'))
                        )
                        ->form([
                            Forms\Components\TextInput::make('receipt_number')
                                ->required()
                                ->label('Receipt Number')
                                ->default(fn ($record) => $record->receipt_number),
                            Forms\Components\Textarea::make('finance_notes')
                                ->label('Finance Notes')
                                ->default(fn ($record) => $record->finance_notes),
                        ])
                        ->action(function ($record, array $data): void {
                            try {
                                DB::beginTransaction();

                                // Update device retrieval with finance approval
                                $record->update([
                                    'receipt_number' => $data['receipt_number'],
                                    'finance_notes' => $data['finance_notes'] ?? null,
                                    'finance_approval_date' => now(),
                                    'finance_approved_by' => auth()->id(),
                                    'payment_status' => 'PD', // Now changing to Paid
                                ]);

                                DB::commit();

                                Notification::make()
                                    ->success()
                                    ->title('Payment Approved')
                                    ->body('The payment has been approved.')
                                    ->send();

                            } catch (\Exception $e) {
                                DB::rollBack();
                                Log::error('Finance approval failed', [
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString(),
                                    'device_retrieval_id' => $record->id
                                ]);

                                Notification::make()
                                    ->danger()
                                    ->title('Error')
                                    ->body('Failed to process payment approval: ' . $e->getMessage())
                                    ->send();
                            }
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Approve Payment')
                        ->modalDescription('Are you sure you want to approve this payment?'),

                    // Download Invoice action
                    Tables\Actions\Action::make('download_invoice')
                        ->label('Download Invoice')
                        ->icon('heroicon-o-document-download')
                        ->color('primary')
                        ->url(fn ($record) => route('invoices.download.retrieval', $record->id))
                        ->openUrlInNewTab()
                        ->visible(fn ($record) =>
                            $record->payment_status === 'PD' &&
                            !empty($record->finance_approval_date)
                        ),
                ])
            ])
            ->defaultSort('date', 'desc')
            ->poll('10s');
    }
}





