<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class ConfirmedAffixed extends Model
{
    use HasFactory;

    protected $table = 'confirmed_affixeds';

    public $incrementing = true;
    protected $keyType = 'int';

    protected $fillable = [
        'date',
        'device_id',
        'boe',
        'sad_number',
        'vehicle_number',
        'regime',
        'destination',
        'destination_id',
        'route_id',
        'long_route_id',
        'manifest_date',
        'agency',
        'agent_contact',
        'truck_number',
        'driver_name',
        'affixing_date',
        'status',
        'allocation_point_id'
    ];

    protected $casts = [
        'date' => 'datetime',
        'manifest_date' => 'date',
        'affixing_date' => 'datetime'
    ];

    protected $appends = ['destination_name'];

    protected static function boot()
    {
        parent::boot();

        // Handle cleanup when a ConfirmedAffixed record is deleted
        static::deleting(function ($confirmedAffixed) {
            // Delete related assign_to_agents record if it exists
            DB::table('assign_to_agents')
                ->where('device_id', $confirmedAffixed->device_id)
                ->delete();

            // If the record is being deleted after affixing, ensure there's a device retrieval record
            if ($confirmedAffixed->status === 'AFFIXED' && !DeviceRetrieval::where('device_id', $confirmedAffixed->device_id)->exists()) {
                DeviceRetrieval::create([
                    'date' => now(),
                    'device_id' => $confirmedAffixed->device_id,
                    'boe' => $confirmedAffixed->boe,
                    'vehicle_number' => $confirmedAffixed->vehicle_number,
                    'regime' => $confirmedAffixed->regime,
                    'destination' => $confirmedAffixed->destination,
                    'route_id' => $confirmedAffixed->route_id,
                    'long_route_id' => $confirmedAffixed->long_route_id,
                    'manifest_date' => $confirmedAffixed->manifest_date,
                    'agency' => $confirmedAffixed->agency,
                    'agent_contact' => $confirmedAffixed->agent_contact,
                    'truck_number' => $confirmedAffixed->truck_number,
                    'driver_name' => $confirmedAffixed->driver_name,
                    'affixing_date' => $confirmedAffixed->affixing_date,
                    'allocation_point_id' => $confirmedAffixed->allocation_point_id,
                    'retrieval_status' => 'NOT_RETRIEVED',
                    'transfer_status' => 'pending'
                ]);
            }
        });
    }

    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id');
    }

    public function route(): BelongsTo
    {
        return $this->belongsTo(Route::class);
    }

    public function longRoute(): BelongsTo
    {
        return $this->belongsTo(LongRoute::class);
    }

    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class, 'destination_id');
    }

    public function allocationPoint(): BelongsTo
    {
        return $this->belongsTo(AllocationPoint::class);
    }

    public function getDestinationNameAttribute()
    {
        // If we have a destination_id and the relationship exists, use that
        if ($this->destination_id) {
            $destination = $this->destination()->first();
            return $destination ? $destination->name : null;
        }
        // Fall back to the string destination field for legacy data
        return $this->attributes['destination'] ?? null;
    }
}
