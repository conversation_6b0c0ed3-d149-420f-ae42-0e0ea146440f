<?php
//Not using this file
namespace App\Filament\Resources\ConfirmedAffixedResource\Pages;

use App\Filament\Resources\ConfirmedAffixedResource;
use App\Models\ConfirmedAffixed;
use App\Models\LongRoute;
use App\Models\Route;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Forms;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

class ViewConfirmedAffixed extends ViewRecord
{
    protected static string $resource = ConfirmedAffixedResource::class;

    protected function getViewData(): array
    {
        $data = parent::getViewData();
        $user = auth()->user();

        // Check if user is a Retrieval Officer
        if ($user?->hasRole('Retrieval Officer')) {
            // Get all permissions that start with 'view_destination_'
            $destinationPermissions = $user->permissions
                ->filter(fn ($permission) => str_starts_with($permission->name, 'view_destination_'))
                ->map(fn ($permission) => Str::after($permission->name, 'view_destination_'))
                ->toArray();

            // If user has destination permissions, check if they can view this record
            if (!empty($destinationPermissions)) {
                // Convert permission slugs to possible destination names
                $possibleDestinations = [];

                foreach ($destinationPermissions as $slug) {
                    $possibleDestinations[] = $slug;
                    $possibleDestinations[] = ucfirst($slug);
                    $possibleDestinations[] = strtoupper($slug);
                    $possibleDestinations[] = Str::title($slug);
                    $possibleDestinations[] = Str::title(str_replace('-', ' ', $slug));
                }

                // Remove duplicates
                $possibleDestinations = array_unique($possibleDestinations);

                // Check if the record's destination matches any of the possible destinations
                $recordDestination = $this->record->destination;
                $destinationName = $this->record->destination?->name ?? $recordDestination;

                if (!in_array($destinationName, $possibleDestinations)) {
                    // If not authorized, redirect to the list page
                    $this->redirect(ConfirmedAffixedResource::getUrl());
                }
            } else {
                // If no destination permissions, redirect to the list page
                $this->redirect(ConfirmedAffixedResource::getUrl());
            }
        }

        return $data;
    }

    public function table(Table $table): Table
    {
        $table = parent::table($table);
        $user = auth()->user();

        // For Retrieval Officer, filter by destination permissions
        if ($user?->hasRole('Retrieval Officer')) {
            // Get all permissions that start with 'view_destination_'
            $destinationPermissions = $user->permissions
                ->filter(fn ($permission) => str_starts_with($permission->name, 'view_destination_'))
                ->map(fn ($permission) => Str::after($permission->name, 'view_destination_'))
                ->toArray();

            // If user has destination permissions, filter by those
            if (!empty($destinationPermissions)) {
                // Convert permission slugs to possible destination names
                $possibleDestinations = [];

                foreach ($destinationPermissions as $slug) {
                    $possibleDestinations[] = $slug;
                    $possibleDestinations[] = ucfirst($slug);
                    $possibleDestinations[] = strtoupper($slug);
                    $possibleDestinations[] = Str::title($slug);
                    $possibleDestinations[] = Str::title(str_replace('-', ' ', $slug));
                }

                // Remove duplicates
                $possibleDestinations = array_unique($possibleDestinations);

                // Filter table query to only include records with matching destinations
                $table->modifyQueryUsing(function (Builder $query) use ($possibleDestinations) {
                    $query->where(function ($query) use ($possibleDestinations) {
                        $query->whereIn('destination', $possibleDestinations)
                            ->orWhereHas('destination', function ($subQuery) use ($possibleDestinations) {
                                $subQuery->whereIn('name', $possibleDestinations);
                            });
                    });
                });
            }
        }

        return $table;
    }
}


