<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ConfirmedAffixed extends Model
{
    use HasFactory;

    protected $table = 'confirmed_affixeds';

    public $incrementing = true;
    protected $keyType = 'int';

    protected $fillable = [
        'date',
        'device_id',
        'boe',
        'sad_number',
        'vehicle_number',
        'regime',
        'destination',
        'destination_id',
        'route_id',
        'long_route_id',
        'manifest_date',
        'agency',
        'agent_contact',
        'truck_number',
        'driver_name',
        'affixing_date',
        'status',
        'allocation_point_id'
    ];

    protected $casts = [
        'date' => 'datetime',
        'manifest_date' => 'date',
        'affixing_date' => 'datetime'
    ];

    protected $appends = ['destination_name'];

    protected static function booted()
    {
        parent::booted();

        // Step 1: Add Global Scope for Destination-Based Access Control
        static::addGlobalScope('destination-access', function (Builder $builder) {
            $user = auth()->user();

            if (!$user) {
                Log::info('ConfirmedAffixed Global Scope: No authenticated user found');
                return;
            }

            Log::info('ConfirmedAffixed Global Scope: Processing for user', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_roles' => $user->roles->pluck('name')->toArray()
            ]);

            // Super Admin, Warehouse Manager, and Affixing Officer can see all confirmed affixed records
            if ($user->hasRole(['Super Admin', 'Warehouse Manager', 'Affixing Officer'])) {
                Log::info('ConfirmedAffixed Global Scope: User has admin access, no filtering applied', [
                    'user_id' => $user->id,
                    'roles' => $user->roles->pluck('name')->toArray()
                ]);
                return;
            }

            // For Retrieval Officer, filter by destination permissions
            if ($user->hasRole('Retrieval Officer')) {
                Log::info('ConfirmedAffixed Global Scope: Processing Retrieval Officer access', [
                    'user_id' => $user->id
                ]);

                $permissions = $user->permissions->pluck('name')->toArray();
                Log::info('ConfirmedAffixed Global Scope: User permissions', [
                    'user_id' => $user->id,
                    'all_permissions' => $permissions
                ]);

                $destinationPermissions = array_filter($permissions, function ($permission) {
                    return Str::startsWith($permission, 'view_destination_');
                });

                Log::info('ConfirmedAffixed Global Scope: Destination permissions found', [
                    'user_id' => $user->id,
                    'destination_permissions' => $destinationPermissions
                ]);

                $destinationSlugs = array_map(function ($permission) {
                    return Str::after($permission, 'view_destination_');
                }, $destinationPermissions);

                Log::info('ConfirmedAffixed Global Scope: Destination slugs extracted', [
                    'user_id' => $user->id,
                    'destination_slugs' => $destinationSlugs
                ]);

                if (!empty($destinationSlugs)) {
                    // Convert permission slugs to possible destination names
                    $possibleDestinations = [];

                    foreach ($destinationSlugs as $slug) {
                        $possibleDestinations[] = $slug;                     // Original slug
                        $possibleDestinations[] = ucfirst($slug);            // Capitalized
                        $possibleDestinations[] = strtoupper($slug);         // Uppercase
                        $possibleDestinations[] = Str::title($slug);         // Title case
                        $possibleDestinations[] = Str::title(str_replace('-', ' ', $slug)); // With spaces
                    }

                    $possibleDestinations = array_unique($possibleDestinations);

                    Log::info('ConfirmedAffixed Global Scope: Possible destination variations generated', [
                        'user_id' => $user->id,
                        'possible_destinations' => $possibleDestinations
                    ]);

                    $builder->where(function ($query) use ($possibleDestinations, $user) {
                        $query->whereIn('destination', $possibleDestinations)
                            ->orWhereHas('destination', function ($subQuery) use ($possibleDestinations) {
                                $subQuery->whereIn('name', $possibleDestinations);
                            });

                        Log::info('ConfirmedAffixed Global Scope: Applied destination filtering', [
                            'user_id' => $user->id,
                            'filter_destinations' => $possibleDestinations
                        ]);
                    });
                } else {
                    Log::warning('ConfirmedAffixed Global Scope: Retrieval Officer has no destination permissions, showing no records', [
                        'user_id' => $user->id
                    ]);
                    $builder->where('id', 0); // Show nothing if no permissions
                }

                return;
            }

            // Default: show nothing for other roles
            Log::info('ConfirmedAffixed Global Scope: User has no recognized role, showing no records', [
                'user_id' => $user->id,
                'roles' => $user->roles->pluck('name')->toArray()
            ]);
            $builder->where('id', 0);
        });

        // Handle cleanup when a ConfirmedAffixed record is deleted
        static::deleting(function ($confirmedAffixed) {
            Log::info('ConfirmedAffixed: Deleting record', [
                'confirmed_affixed_id' => $confirmedAffixed->id,
                'device_id' => $confirmedAffixed->device_id,
                'status' => $confirmedAffixed->status
            ]);

            // Delete related assign_to_agents record if it exists
            DB::table('assign_to_agents')
                ->where('device_id', $confirmedAffixed->device_id)
                ->delete();

            // If the record is being deleted after affixing, ensure there's a device retrieval record
            if ($confirmedAffixed->status === 'AFFIXED' && !DeviceRetrieval::where('device_id', $confirmedAffixed->device_id)->exists()) {
                Log::info('ConfirmedAffixed: Creating DeviceRetrieval record for affixed device', [
                    'device_id' => $confirmedAffixed->device_id,
                    'destination' => $confirmedAffixed->destination
                ]);

                DeviceRetrieval::create([
                    'date' => now(),
                    'device_id' => $confirmedAffixed->device_id,
                    'boe' => $confirmedAffixed->boe,
                    'vehicle_number' => $confirmedAffixed->vehicle_number,
                    'regime' => $confirmedAffixed->regime,
                    'destination' => $confirmedAffixed->destination,
                    'route_id' => $confirmedAffixed->route_id,
                    'long_route_id' => $confirmedAffixed->long_route_id,
                    'manifest_date' => $confirmedAffixed->manifest_date,
                    'agency' => $confirmedAffixed->agency,
                    'agent_contact' => $confirmedAffixed->agent_contact,
                    'truck_number' => $confirmedAffixed->truck_number,
                    'driver_name' => $confirmedAffixed->driver_name,
                    'affixing_date' => $confirmedAffixed->affixing_date,
                    'allocation_point_id' => $confirmedAffixed->allocation_point_id,
                    'retrieval_status' => 'NOT_RETRIEVED',
                    'transfer_status' => 'pending'
                ]);
            }
        });
    }

    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id');
    }

    public function route(): BelongsTo
    {
        return $this->belongsTo(Route::class);
    }

    public function longRoute(): BelongsTo
    {
        return $this->belongsTo(LongRoute::class);
    }

    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class, 'destination_id')
            ->withDefault(['name' => 'Unknown']); // Provides fallback if relation is missing
    }

    public function allocationPoint(): BelongsTo
    {
        return $this->belongsTo(AllocationPoint::class);
    }

    public function getDestinationNameAttribute()
    {
        // If we have a destination_id and the relationship exists, use that
        if ($this->destination_id) {
            $destination = $this->destination()->first();
            return $destination ? $destination->name : null;
        }
        // Fall back to the string destination field for legacy data
        return $this->attributes['destination'] ?? null;
    }
}
