<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_allocation_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('allocation_point_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['user_id', 'allocation_point_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_allocation_points');
    }
};
