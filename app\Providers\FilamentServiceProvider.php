<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Panel;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Filament::serving(function () {
            Panel::configureUsing(function (Panel $panel) {
                $panel
                    ->id('admin') // Ensure this matches config
                    ->path('admin')
                    // Other configurations...
                ;
            });
        });
    }
}
