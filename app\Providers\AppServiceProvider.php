<?php

namespace App\Providers;

use App\Models\Device;
use App\Models\DeviceRetrieval;
use App\Observers\DeviceObserver;
use App\Observers\DeviceRetrievalOverstayObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Device::observe(DeviceObserver::class);
        DeviceRetrieval::observe(DeviceRetrievalOverstayObserver::class);
    }
}

