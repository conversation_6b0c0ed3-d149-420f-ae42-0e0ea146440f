<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ConfirmedAffixedResource\Pages;
use App\Models\ConfirmedAffixed;
use App\Models\DeviceRetrieval;
use App\Models\LongRoute;
use App\Models\Route;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class ConfirmedAffixedResource extends Resource
{
    protected static ?string $model = ConfirmedAffixed::class;

    protected static ?string $navigationIcon = 'heroicon-o-check-badge';
    protected static ?string $navigationLabel = 'Confirmed Affixed';
    protected static ?string $navigationGroup = 'Device Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('device_id')
                    ->relationship('device', 'device_id')
                    ->required()
                    ->searchable(),
                Forms\Components\Select::make('route_id')
                    ->label('Route')
                    ->options(Route::pluck('name', 'id'))
                    ->searchable(),
                   // ->required(),
                Forms\Components\Select::make('long_route_id')
                    ->label('Long Route')
                    ->options(LongRoute::pluck('name', 'id'))
                    ->searchable(),
                Forms\Components\DateTimePicker::make('affixing_date'),
                Forms\Components\TextInput::make('boe')
                    ->label('SAD/T1')
                    ->required(),
                Forms\Components\TextInput::make('vehicle_number')
                    ->required(),
                Forms\Components\TextInput::make('regime')
                    ->required(),
                Forms\Components\DatePicker::make('manifest_date'),
                Forms\Components\Select::make('destination')
                    ->label('Destination')
                    ->options(function () {
                        return \App\Models\Destination::pluck('name', 'name')->toArray();
                    })
                    ->required()
                    ->searchable(),
                //Forms\Components\Select::make('destination_id')
                   // ->relationship('destination', 'name')
                  //  ->label('Destination ID')
                   // ->searchable(),
                Forms\Components\TextInput::make('agency'),
                Forms\Components\TextInput::make('agent_contact'),
                Forms\Components\TextInput::make('truck_number'),
                Forms\Components\TextInput::make('driver_name'),
                Forms\Components\Select::make('status')
                    ->options([
                        'PENDING' => 'Pending',
                        'AFFIXED' => 'Affixed',
                        'COMPLETED' => 'Completed',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('device.device_id')
                    ->label('Device ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('boe')
                    ->label('SAD/T1')
                    ->searchable(),
                Tables\Columns\TextColumn::make('vehicle_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('destination')
                    ->label('Destination')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('regime')
                    ->searchable(),
                Tables\Columns\TextColumn::make('route.name')
                    ->label('Route')
                    ->searchable(),
                Tables\Columns\TextColumn::make('longRoute.name')
                    ->label('Long Route')
                    ->searchable(),
                Tables\Columns\TextColumn::make('manifest_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('agency')
                    ->searchable(),
                Tables\Columns\TextColumn::make('agent_contact'),
                Tables\Columns\TextColumn::make('truck_number'),
                Tables\Columns\TextColumn::make('driver_name')
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'PENDING' => 'Pending',
                        'AFFIXED' => 'Affixed',
                        'COMPLETED' => 'Completed',
                    ])
            ])
            ->actions([
                Tables\Actions\Action::make('pickForAffixing')
                    ->label('Pick for Affixing')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->form([
                        Forms\Components\DateTimePicker::make('affixing_date')
                            ->label('Affixing Date')
                            ->required()
                            ->default(now())
                    ])
                    ->requiresConfirmation()
                    ->modalHeading('Pick Device for Affixing')
                    ->modalDescription('Are you sure you want to pick this device for affixing?')
                    ->modalSubmitActionLabel('Yes, Pick for Affixing')
                    ->action(function (ConfirmedAffixed $record, array $data): void {
                        try {
                            DB::beginTransaction();

                            // Step 1: Create device retrieval record
                            $deviceRetrieval = [
                                'date' => now(),
                                'device_id' => $record->device_id,
                                'boe' => $record->boe,
                                'vehicle_number' => $record->vehicle_number,
                                'regime' => $record->regime,
                                'destination' => $record->destination,
                                'route_id' => $record->route_id,
                                'long_route_id' => $record->long_route_id,
                                'manifest_date' => $record->manifest_date,
                                'agency' => $record->agency,
                                'agent_contact' => $record->agent_contact,
                                'truck_number' => $record->truck_number,
                                'driver_name' => $record->driver_name,
                                'affixing_date' => $data['affixing_date'],
                                'allocation_point_id' => $record->allocation_point_id,
                                'retrieval_status' => 'NOT_RETRIEVED',
                                'transfer_status' => 'pending',
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];

                            // Step 2: Insert device retrieval record
                            DB::table('device_retrievals')->insert($deviceRetrieval);

                            // Step 3: Delete from assign_to_agents table
                            DB::table('assign_to_agents')
                                ->where('device_id', $record->device_id)
                                ->delete();

                            // Step 4: Update confirmed_affixeds table
                            DB::table('confirmed_affixeds')
                                ->where('id', $record->id)
                                ->update([
                                    'status' => 'AFFIXED',
                                    'affixing_date' => $data['affixing_date'],
                                    'updated_at' => now()
                                ]);
                                 // Step 5: delete confirmed_affixeds table

                                DB::table('confirmed_affixeds')
                                ->where('id', $record->id)
                                ->delete();

                            DB::commit();

                            Notification::make()
                                ->success()
                                ->title('Device picked for affixing')
                                ->send();

                        } catch (\Exception $e) {
                            DB::rollBack();
                            Log::error('Error in pickForAffixing', [
                                'error' => $e->getMessage(),
                                'record_id' => $record->id
                            ]);

                            Notification::make()
                                ->danger()
                                ->title('Error picking device for affixing')
                                ->body($e->getMessage())
                                ->send();
                        }
                    })
                    ->visible(fn (ConfirmedAffixed $record): bool => $record->status !== 'AFFIXED'),
                Tables\Actions\Action::make('returnData')
                    ->label('Return Data')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->form([
                        Forms\Components\Textarea::make('return_note')
                            ->label('Reason for Return')
                            ->required()
                            ->maxLength(1000)
                    ])
                    ->requiresConfirmation()
                    ->modalHeading('Return Data to Data Entry')
                    ->modalDescription('Are you sure you want to return this data? This will move the record back to data entry.')
                    ->modalSubmitActionLabel('Yes, Return Data')
                    ->action(function (ConfirmedAffixed $record, array $data): void {
                        try {
                            DB::beginTransaction();

                            // Get the device
                            $device = $record->device;

                            if (!$device) {
                                throw new \Exception('Device not found');
                            }

                            // Get the original allocation point ID
                            $allocationPointId = $record->allocation_point_id;

                            if (!$allocationPointId) {
                                throw new \Exception('Original allocation point not found');
                            }

                            // Check if DataEntryAssignment already exists for this allocation point
                            $existingAssignment = \App\Models\DataEntryAssignment::where('allocation_point_id', $allocationPointId)
                                ->first();

                            if ($existingAssignment) {
                                // Update existing assignment instead of creating new one
                                $existingAssignment->update([
                                    'status' => 'RETURNED',
                                    'notes' => $data['return_note'] . "\n(Previous notes: " . $existingAssignment->notes . ")",
                                    'description' => $existingAssignment->description . "\nReturned from Affixing - BOE: {$record->boe}, Vehicle: {$record->vehicle_number}",
                                    'user_id' => auth()->id()
                                ]);
                            } else {
                                // Create new assignment only if one doesn't exist
                                \App\Models\DataEntryAssignment::create([
                                    'allocation_point_id' => $allocationPointId,
                                    'status' => 'RETURNED',
                                    'notes' => $data['return_note'],
                                    'title' => 'Returned from Affixing',
                                    'description' => "Returned from Affixing - BOE: {$record->boe}, Vehicle: {$record->vehicle_number}",
                                    'user_id' => auth()->id()
                                ]);
                            }

                            // Restore device to original allocation point
                            $device->update([
                                'allocation_point_id' => $allocationPointId,
                                'status' => 'ONLINE'
                            ]);

                            // Delete assign_to_agents record if exists
                            DB::table('assign_to_agents')
                                ->where('device_id', $record->device_id)
                                ->delete();

                            // Delete the confirmed affixed record
                            $record->delete();

                            DB::commit();

                            Notification::make()
                                ->success()
                                ->title('Data returned to data entry successfully')
                                ->send();

                        } catch (\Exception $e) {
                            DB::rollBack();

                            Notification::make()
                                ->danger()
                                ->title('Error returning data')
                                ->body($e->getMessage())
                                ->send();
                        }
                    })
                    ->visible(fn (ConfirmedAffixed $record): bool => $record->status === 'PENDING'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConfirmedAffixeds::route('/'),
            'view' => Pages\ViewConfirmedAffixed::route('/{record}'),
        ];
    }

    public static function canViewAny(): bool
    {
        return auth()->user()?->hasAnyRole([
            'Super Admin',
            'Warehouse Manager',
            'Affixing Officer'
        ]);
    }

    public static function canCreate(): bool
    {
        return auth()->user()?->hasAnyRole([
            'Super Admin',
            'Warehouse Manager',
            'Affixing Officer'
        ]);
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()?->hasAnyRole([
            'Super Admin',
            'Warehouse Manager',
            'Affixing Officer'
        ]);
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()?->hasAnyRole([
            'Super Admin',
            'Warehouse Manager',
            'Affixing Officer'
        ]);
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        $user = auth()->user();

        Log::info('ConfirmedAffixedResource: getEloquentQuery called', [
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_roles' => $user?->roles->pluck('name')->toArray() ?? []
        ]);

        // Super Admin, Warehouse Manager, and Affixing Officer can see all confirmed affixed records
        if ($user?->hasRole(['Super Admin', 'Warehouse Manager', 'Affixing Officer'])) {
            Log::info('ConfirmedAffixedResource: User has admin access, no filtering applied', [
                'user_id' => $user->id,
                'roles' => $user->roles->pluck('name')->toArray()
            ]);
            return $query;
        }

        // For Retrieval Officer, filter by destination permissions
        if ($user?->hasRole('Retrieval Officer')) {
            Log::info('ConfirmedAffixedResource: Processing Retrieval Officer access', [
                'user_id' => $user->id
            ]);

            // Get all permissions that start with 'view_destination_'
            $destinationPermissions = $user->permissions
                ->filter(fn ($permission) => str_starts_with($permission->name, 'view_destination_'))
                ->map(fn ($permission) => Str::after($permission->name, 'view_destination_'))
                ->toArray();

            Log::info('ConfirmedAffixedResource: Destination permissions extracted', [
                'user_id' => $user->id,
                'destination_permissions' => $destinationPermissions,
                'all_permissions' => $user->permissions->pluck('name')->toArray()
            ]);

            // If user has destination permissions, filter by those
            if (!empty($destinationPermissions)) {
                // Convert permission slugs to possible destination names
                $possibleDestinations = [];

                foreach ($destinationPermissions as $slug) {
                    // Add variations of the destination name to check against the database
                    $possibleDestinations[] = $slug;                     // Original slug
                    $possibleDestinations[] = ucfirst($slug);            // First letter capitalized
                    $possibleDestinations[] = strtoupper($slug);         // All uppercase
                    $possibleDestinations[] = Str::title($slug);         // Title case
                    $possibleDestinations[] = Str::title(str_replace('-', ' ', $slug));  // With spaces
                }

                // Remove duplicates
                $possibleDestinations = array_unique($possibleDestinations);

                Log::info('ConfirmedAffixedResource: Possible destination variations generated', [
                    'user_id' => $user->id,
                    'original_slugs' => $destinationPermissions,
                    'possible_destinations' => $possibleDestinations
                ]);

                // Log existing destinations in database for comparison
                $existingDestinations = \App\Models\ConfirmedAffixed::withoutGlobalScopes()
                    ->select('destination')
                    ->distinct()
                    ->whereNotNull('destination')
                    ->pluck('destination')
                    ->toArray();

                $existingDestinationRelations = \App\Models\Destination::pluck('name')->toArray();

                Log::info('ConfirmedAffixedResource: Existing destinations in database', [
                    'user_id' => $user->id,
                    'string_destinations' => $existingDestinations,
                    'destination_relations' => $existingDestinationRelations
                ]);

                // Filter query to only include confirmed affixed records with matching destinations
                return $query->where(function ($query) use ($possibleDestinations, $user) {
                    // Check against the destination column (string)
                    $query->whereIn('destination', $possibleDestinations)
                        // Also check against the destination relationship if it exists
                        ->orWhereHas('destination', function ($subQuery) use ($possibleDestinations) {
                            $subQuery->whereIn('name', $possibleDestinations);
                        });

                    Log::info('ConfirmedAffixedResource: Applied destination filtering', [
                        'user_id' => $user->id,
                        'filter_destinations' => $possibleDestinations
                    ]);
                });
            }

            Log::warning('ConfirmedAffixedResource: Retrieval Officer has no destination permissions, showing no records', [
                'user_id' => $user->id,
                'all_permissions' => $user->permissions->pluck('name')->toArray()
            ]);

            // If no destination permissions, show nothing
            return $query->where('id', 0);
        }

        Log::info('ConfirmedAffixedResource: User has no recognized role, showing no records', [
            'user_id' => $user?->id,
            'roles' => $user?->roles->pluck('name')->toArray() ?? []
        ]);

        // Default: show nothing for other roles
        return $query->where('id', 0);
    }

    protected function getTableQuery(): Builder
    {
        $query = parent::getTableQuery();
        $user = auth()->user();

        Log::info('ConfirmedAffixedResource: getTableQuery called', [
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_roles' => $user?->roles->pluck('name')->toArray() ?? []
        ]);

        // For Retrieval Officer, filter by destination permissions
        if ($user?->hasRole('Retrieval Officer')) {
            Log::info('ConfirmedAffixedResource: getTableQuery processing Retrieval Officer access', [
                'user_id' => $user->id
            ]);

            // Get all permissions that start with 'view_destination_'
            $destinationPermissions = $user->permissions
                ->filter(fn ($permission) => str_starts_with($permission->name, 'view_destination_'))
                ->map(fn ($permission) => Str::after($permission->name, 'view_destination_'))
                ->toArray();

            Log::info('ConfirmedAffixedResource: getTableQuery destination permissions extracted', [
                'user_id' => $user->id,
                'destination_permissions' => $destinationPermissions,
                'all_permissions' => $user->permissions->pluck('name')->toArray()
            ]);

            // If user has destination permissions, filter by those
            if (!empty($destinationPermissions)) {
                // Convert permission slugs to possible destination names
                $possibleDestinations = [];

                foreach ($destinationPermissions as $slug) {
                    // Add variations of the destination name to check against the database
                    $possibleDestinations[] = $slug;
                    $possibleDestinations[] = ucfirst($slug);
                    $possibleDestinations[] = strtoupper($slug);
                    $possibleDestinations[] = Str::title($slug);
                    $possibleDestinations[] = Str::title(str_replace('-', ' ', $slug));
                }

                // Remove duplicates
                $possibleDestinations = array_unique($possibleDestinations);

                Log::info('ConfirmedAffixedResource: getTableQuery possible destination variations generated', [
                    'user_id' => $user->id,
                    'original_slugs' => $destinationPermissions,
                    'possible_destinations' => $possibleDestinations
                ]);

                // Filter query to only include confirmed affixed records with matching destinations
                $query->where(function ($query) use ($possibleDestinations, $user) {
                    // Check against the destination column (string)
                    $query->whereIn('destination', $possibleDestinations)
                        // Also check against the destination relationship if it exists
                        ->orWhereHas('destination', function ($subQuery) use ($possibleDestinations) {
                            $subQuery->whereIn('name', $possibleDestinations);
                        });

                    Log::info('ConfirmedAffixedResource: getTableQuery applied destination filtering', [
                        'user_id' => $user->id,
                        'filter_destinations' => $possibleDestinations
                    ]);
                });
            } else {
                Log::warning('ConfirmedAffixedResource: getTableQuery Retrieval Officer has no destination permissions, showing no records', [
                    'user_id' => $user->id,
                    'all_permissions' => $user->permissions->pluck('name')->toArray()
                ]);

                // If no destination permissions, show nothing
                $query->where('id', 0);
            }
        }

        return $query;
    }
}

